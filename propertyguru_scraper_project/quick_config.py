#!/usr/bin/env python3
"""
快速配置生成工具 - 简化版
"""

import json
import os
from urllib.parse import urlparse, parse_qs, unquote
from datetime import datetime


def extract_config_from_url(url):
    """从URL提取配置参数"""
    try:
        parsed = urlparse(url)
        params = parse_qs(parsed.query)
        
        # 基础信息
        config = {
            "listingType": "rent" if "property-for-rent" in parsed.path else "sale",
            "isCommercial": params.get('isCommercial', ['false'])[0],
            "propertyTypeGroup": "N",
            "propertyTypeCode": ["APT", "CLUS", "CONDO", "EXCON", "WALK"]
        }
        
        # 特定房产ID
        if 'propertyId' in params:
            config['propertyId'] = params['propertyId'][0]

        # MRT站点
        if 'mrtStations' in params:
            config['mrtStations'] = params['mrtStations']

        # 自由文本
        if 'freetext' in params:
            config['freetext'] = params['freetext'][0]
            config['_freetextDisplay'] = params['freetext'][0]
        elif '_freetextDisplay' in params:
            config['_freetextDisplay'] = params['_freetextDisplay'][0]

        # 卧室
        if 'bedrooms' in params:
            config['bedrooms'] = params['bedrooms']

        # 价格
        if 'minPrice' in params:
            config['minPrice'] = params['minPrice'][0]
        if 'maxPrice' in params:
            config['maxPrice'] = params['maxPrice'][0]

        # 地区设置
        if 'locale' in params:
            config['locale'] = params['locale'][0]
        
        return config
        
    except Exception as e:
        print(f"❌ URL解析错误: {e}")
        return None


def create_config_interactive():
    """交互式创建配置"""
    print("🔧 PropertyGuru配置快速生成器")
    print("=" * 50)
    
    # 获取URL
    url = input("请粘贴PropertyGuru搜索URL: ").strip()
    if not url:
        print("❌ URL不能为空")
        return
    
    # 解析URL
    base_config = extract_config_from_url(url)
    if not base_config:
        return
    
    # 获取配置信息
    print("\n📋 请输入配置信息:")
    name = input("配置名称: ").strip()
    description = input("配置描述: ").strip()
    filename = input("文件名 (不含.json): ").strip()
    
    # 构建完整配置
    config = {
        "name": name,
        "description": description,
        **base_config,
        "notes": [
            f"基于URL生成: {url}",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "可手动编辑此配置文件进行自定义"
        ]
    }
    
    # 显示配置预览
    print("\n📄 配置预览:")
    print("-" * 40)
    print(f"名称: {config['name']}")
    print(f"类型: {config['listingType']}")
    print(f"地铁站: {len(config.get('mrtStations', []))} 个")
    if config.get('mrtStations'):
        print(f"  {', '.join(config['mrtStations'])}")
    print(f"卧室: {', '.join(config.get('bedrooms', ['未指定']))}")
    if config.get('minPrice') or config.get('maxPrice'):
        min_p = config.get('minPrice', '无限制')
        max_p = config.get('maxPrice', '无限制')
        print(f"价格: S${min_p} - S${max_p}")
    
    # 确认保存
    save = input("\n是否保存此配置? (y/n): ").lower().strip()
    if save != 'y':
        print("❌ 配置未保存")
        return
    
    # 保存文件
    if not filename:
        print("❌ 文件名不能为空")
        return
    
    config_path = f"configs/{filename}.json"
    os.makedirs("configs", exist_ok=True)
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已保存为: {config_path}")
        print("🎯 现在可以在主程序中选择此配置进行爬取")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")


def quick_create_from_args():
    """从命令行参数快速创建"""
    import sys
    
    if len(sys.argv) < 4:
        print("用法: python3 quick_config.py <URL> <配置名称> <文件名>")
        print("示例: python3 quick_config.py 'https://...' '租赁-西南部' 'rent_southwest'")
        return
    
    url = sys.argv[1]
    name = sys.argv[2]
    filename = sys.argv[3]
    description = sys.argv[4] if len(sys.argv) > 4 else f"基于URL生成的配置: {name}"
    
    # 解析URL
    base_config = extract_config_from_url(url)
    if not base_config:
        return
    
    # 构建配置
    config = {
        "name": name,
        "description": description,
        **base_config,
        "notes": [
            f"基于URL生成: {url}",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ]
    }
    
    # 保存
    config_path = f"configs/{filename}.json"
    os.makedirs("configs", exist_ok=True)
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已保存为: {config_path}")
        
        # 显示摘要
        print(f"📋 配置摘要:")
        print(f"  名称: {config['name']}")
        print(f"  类型: {config['listingType']}")
        print(f"  地铁站: {len(config.get('mrtStations', []))} 个")
        print(f"  卧室: {', '.join(config.get('bedrooms', ['未指定']))}")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 1:
        # 交互式模式
        create_config_interactive()
    else:
        # 命令行模式
        quick_create_from_args()
