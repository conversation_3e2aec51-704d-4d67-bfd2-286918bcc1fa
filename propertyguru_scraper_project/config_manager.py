#!/usr/bin/env python3
"""
PropertyGuru配置管理器 - 便捷的配置创建和管理工具
"""

import json
import os
import sys
from urllib.parse import urlparse, parse_qs
from datetime import datetime


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir="configs"):
        self.config_dir = config_dir
        os.makedirs(config_dir, exist_ok=True)
    
    def parse_url(self, url):
        """解析PropertyGuru URL"""
        try:
            parsed = urlparse(url)
            params = parse_qs(parsed.query)
            
            config = {
                "listingType": "rent" if "property-for-rent" in parsed.path else "sale",
                "isCommercial": params.get('isCommercial', ['false'])[0],
                "propertyTypeGroup": "N",
                "propertyTypeCode": ["APT", "CLUS", "CONDO", "EXCON", "WALK"]
            }
            
            # 特定房产ID
            if 'propertyId' in params:
                config['propertyId'] = params['propertyId'][0]

            # MRT站点
            if 'mrtStations' in params:
                config['mrtStations'] = params['mrtStations']

            # 自由文本
            if 'freetext' in params:
                config['freetext'] = params['freetext'][0]
                config['_freetextDisplay'] = params['freetext'][0]
            elif '_freetextDisplay' in params:
                config['_freetextDisplay'] = params['_freetextDisplay'][0]

            # 卧室
            if 'bedrooms' in params:
                config['bedrooms'] = params['bedrooms']

            # 价格
            for price_field in ['minPrice', 'maxPrice', 'minRent', 'maxRent']:
                if price_field in params:
                    config[price_field] = params[price_field][0]

            # 地区设置
            if 'locale' in params:
                config['locale'] = params['locale'][0]
            
            return config
            
        except Exception as e:
            raise ValueError(f"URL解析失败: {e}")
    
    def create_config(self, url, name, description="", filename=""):
        """从URL创建配置"""
        base_config = self.parse_url(url)
        
        config = {
            "name": name,
            "description": description or f"基于URL生成的配置: {name}",
            **base_config,
            "notes": [
                f"基于URL生成: {url}",
                f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "可手动编辑此配置文件进行自定义"
            ]
        }
        
        return config
    
    def save_config(self, config, filename):
        """保存配置到文件"""
        config_path = os.path.join(self.config_dir, f"{filename}.json")
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        return config_path
    
    def list_configs(self):
        """列出所有配置"""
        configs = []
        for filename in os.listdir(self.config_dir):
            if filename.endswith('.json'):
                config_path = os.path.join(self.config_dir, filename)
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        configs.append({
                            'filename': filename[:-5],
                            'name': config.get('name', filename[:-5]),
                            'type': config.get('listingType', 'unknown'),
                            'mrt_count': len(config.get('mrtStations', [])),
                            'bedrooms': config.get('bedrooms', [])
                        })
                except:
                    continue
        return configs
    
    def interactive_create(self):
        """交互式创建配置"""
        print("🔧 PropertyGuru配置管理器")
        print("=" * 50)
        
        # 获取URL
        print("请粘贴PropertyGuru搜索URL:")
        url = input("URL: ").strip()
        if not url:
            print("❌ URL不能为空")
            return
        
        # 解析URL预览
        try:
            base_config = self.parse_url(url)
            print(f"\n📋 URL解析结果:")
            print(f"  类型: {base_config['listingType']}")
            print(f"  地铁站: {len(base_config.get('mrtStations', []))} 个")
            if base_config.get('mrtStations'):
                print(f"    {', '.join(base_config['mrtStations'])}")
            print(f"  卧室: {', '.join(base_config.get('bedrooms', ['未指定']))}")
            if base_config.get('minPrice') or base_config.get('maxPrice'):
                min_p = base_config.get('minPrice', '无限制')
                max_p = base_config.get('maxPrice', '无限制')
                print(f"  价格: S${min_p} - S${max_p}")
        except Exception as e:
            print(f"❌ URL解析失败: {e}")
            return
        
        print("\n📝 请输入配置信息:")
        name = input("配置名称: ").strip()
        description = input("配置描述 (可选): ").strip()
        filename = input("文件名 (不含.json): ").strip()
        
        if not name or not filename:
            print("❌ 配置名称和文件名不能为空")
            return
        
        # 创建配置
        config = self.create_config(url, name, description, filename)
        
        # 保存配置
        try:
            config_path = self.save_config(config, filename)
            print(f"\n✅ 配置已保存为: {config_path}")
            print("🎯 现在可以在主程序中选择此配置进行爬取")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def quick_create(self, url, name, filename, description=""):
        """快速创建配置"""
        try:
            config = self.create_config(url, name, description, filename)
            config_path = self.save_config(config, filename)
            
            print(f"✅ 配置已创建: {config_path}")
            print(f"📋 配置摘要:")
            print(f"  名称: {config['name']}")
            print(f"  类型: {config['listingType']}")
            print(f"  地铁站: {len(config.get('mrtStations', []))} 个")
            print(f"  卧室: {', '.join(config.get('bedrooms', ['未指定']))}")
            
            return True
        except Exception as e:
            print(f"❌ 创建失败: {e}")
            return False


def main():
    """主函数"""
    manager = ConfigManager()
    
    if len(sys.argv) == 1:
        # 交互式模式
        manager.interactive_create()
    elif len(sys.argv) >= 4:
        # 命令行模式: python config_manager.py <URL> <名称> <文件名> [描述]
        url = sys.argv[1]
        name = sys.argv[2]
        filename = sys.argv[3]
        description = sys.argv[4] if len(sys.argv) > 4 else ""
        
        manager.quick_create(url, name, filename, description)
    else:
        print("用法:")
        print("  交互式模式: python3 config_manager.py")
        print("  命令行模式: python3 config_manager.py <URL> <配置名称> <文件名> [描述]")
        print()
        print("示例:")
        print("  python3 config_manager.py 'https://www.propertyguru.com.sg/...' '租赁-西南部' 'rent_southwest'")


if __name__ == "__main__":
    main()
